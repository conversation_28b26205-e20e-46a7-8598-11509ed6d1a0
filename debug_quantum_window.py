#!/usr/bin/env python3
"""调试QuantumSeek窗口状态和截图问题"""

import logging
from desk_pilot.core import DeskPilotCore
import pygetwindow as gw
import time
import base64
from PIL import Image
import io

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s:%(name)s:%(message)s')

def debug_window_state():
    """调试窗口状态"""
    print('🔍 调试QuantumSeek窗口状态...')
    
    # 查找QuantumSeek窗口
    windows = gw.getWindowsWithTitle('QuantumSeek')
    if not windows:
        print('❌ 未找到QuantumSeek窗口')
        return None
    
    window = windows[0]
    print(f'🎯 找到窗口: {window.title}')
    print(f'📍 窗口位置: ({window.left}, {window.top})')
    print(f'📏 窗口尺寸: {window.width} x {window.height}')
    print(f'👁️ 窗口可见: {window.visible}')
    print(f'📦 窗口最小化: {window.isMinimized}')
    print(f'📈 窗口最大化: {window.isMaximized}')
    print(f'⚡ 窗口激活: {window.isActive}')
    
    # 尝试激活窗口
    print('\n🔄 尝试激活窗口...')
    try:
        if not window.isActive:
            window.activate()
            time.sleep(1)
            print(f'✅ 窗口激活状态: {window.isActive}')
        else:
            print('✅ 窗口已经是激活状态')
    except Exception as e:
        print(f'❌ 激活窗口失败: {e}')
    
    # 尝试恢复窗口（如果最小化）
    if window.isMinimized:
        print('🔄 尝试恢复最小化窗口...')
        try:
            window.restore()
            time.sleep(1)
            print(f'✅ 窗口恢复状态: 最小化={window.isMinimized}')
        except Exception as e:
            print(f'❌ 恢复窗口失败: {e}')
    
    return window

def test_multiple_capture_methods(window):
    """测试多种截图方法"""
    print('\n🔬 测试多种截图方法...')
    
    core = DeskPilotCore(visual_mode=True)
    
    # 方法1: 使用DeskPilot的capture_window
    print('\n📸 方法1: DeskPilot capture_window')
    result1 = core.capture_window(window.title)
    if result1["success"]:
        save_screenshot(result1["screenshot_base64"], "method1_deskpilot.png")
        analyze_screenshot_basic("method1_deskpilot.png")
    else:
        print(f'❌ 方法1失败: {result1["error"]}')
    
    # 方法2: 使用PIL ImageGrab直接截图窗口区域
    print('\n📸 方法2: PIL ImageGrab区域截图')
    try:
        from PIL import ImageGrab
        left, top, width, height = window.left, window.top, window.width, window.height
        screenshot2 = ImageGrab.grab(bbox=(left, top, left + width, top + height))
        screenshot2.save("method2_pil.png")
        print('✅ PIL区域截图成功')
        analyze_screenshot_basic("method2_pil.png")
    except Exception as e:
        print(f'❌ 方法2失败: {e}')
    
    # 方法3: 使用pyautogui截图
    print('\n📸 方法3: pyautogui区域截图')
    try:
        import pyautogui
        left, top, width, height = window.left, window.top, window.width, window.height
        screenshot3 = pyautogui.screenshot(region=(left, top, width, height))
        screenshot3.save("method3_pyautogui.png")
        print('✅ pyautogui区域截图成功')
        analyze_screenshot_basic("method3_pyautogui.png")
    except Exception as e:
        print(f'❌ 方法3失败: {e}')
    
    # 方法4: 全屏截图然后裁剪
    print('\n📸 方法4: 全屏截图后裁剪')
    try:
        import pyautogui
        full_screenshot = pyautogui.screenshot()
        left, top, width, height = window.left, window.top, window.width, window.height
        cropped = full_screenshot.crop((left, top, left + width, top + height))
        cropped.save("method4_fullscreen_crop.png")
        print('✅ 全屏截图裁剪成功')
        analyze_screenshot_basic("method4_fullscreen_crop.png")
    except Exception as e:
        print(f'❌ 方法4失败: {e}')

def save_screenshot(base64_data, filename):
    """保存base64截图到文件"""
    try:
        with open(filename, 'wb') as f:
            f.write(base64.b64decode(base64_data))
        print(f'  💾 截图已保存: {filename}')
    except Exception as e:
        print(f'  ❌ 保存失败: {e}')

def analyze_screenshot_basic(filename):
    """基本截图分析"""
    try:
        with Image.open(filename) as img:
            width, height = img.size
            pixels = list(img.getdata())
            total_pixels = len(pixels)
            
            # 计算非黑色像素
            non_black_pixels = 0
            for pixel in pixels:
                r, g, b = pixel[:3]
                if r > 10 or g > 10 or b > 10:
                    non_black_pixels += 1
            
            non_black_ratio = non_black_pixels / total_pixels
            print(f'  📊 尺寸: {width}x{height}, 非黑色像素: {non_black_ratio:.2%}')
            
            if non_black_ratio < 0.1:
                print('  ⚠️ 主要是黑色，可能截图失败')
            elif non_black_ratio > 0.3:
                print('  ✅ 有丰富内容')
            else:
                print('  ℹ️ 有一些内容')
                
    except Exception as e:
        print(f'  ❌ 分析失败: {e}')

def check_window_visibility():
    """检查窗口是否被遮挡"""
    print('\n👁️ 检查窗口可见性...')
    
    try:
        import win32gui
        
        # 获取QuantumSeek窗口句柄
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                if 'QuantumSeek' in window_text:
                    windows.append((hwnd, window_text))
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if windows:
            hwnd, title = windows[0]
            print(f'🎯 找到窗口句柄: {hwnd} - {title}')
            
            # 检查窗口是否可见
            is_visible = win32gui.IsWindowVisible(hwnd)
            print(f'👁️ 窗口可见: {is_visible}')
            
            # 获取窗口矩形
            rect = win32gui.GetWindowRect(hwnd)
            print(f'📐 窗口矩形: {rect}')
            
            # 检查窗口是否在前台
            foreground_hwnd = win32gui.GetForegroundWindow()
            is_foreground = (hwnd == foreground_hwnd)
            print(f'⚡ 窗口在前台: {is_foreground}')
            
            if not is_foreground:
                print('🔄 尝试将窗口置于前台...')
                try:
                    win32gui.SetForegroundWindow(hwnd)
                    time.sleep(0.5)
                    new_foreground = win32gui.GetForegroundWindow()
                    print(f'✅ 前台窗口设置结果: {hwnd == new_foreground}')
                except Exception as e:
                    print(f'❌ 设置前台窗口失败: {e}')
        else:
            print('❌ 未找到QuantumSeek窗口句柄')
            
    except ImportError:
        print('⚠️ win32gui不可用，跳过窗口可见性检查')
    except Exception as e:
        print(f'❌ 检查窗口可见性失败: {e}')

def main():
    """主调试函数"""
    print('🚀 QuantumSeek窗口调试与截图测试')
    print('=' * 60)
    
    try:
        # 1. 调试窗口状态
        window = debug_window_state()
        if not window:
            return
        
        # 2. 检查窗口可见性
        check_window_visibility()
        
        # 3. 测试多种截图方法
        test_multiple_capture_methods(window)
        
        print('\n✅ 调试完成!')
        print('📝 请检查生成的截图文件，比较不同方法的效果')
        
    except Exception as e:
        print(f'❌ 调试过程中发生错误: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
