"""
DeskPilot Core - 核心桌面交互功能实现

提供窗口截图、UI操作、窗口管理等核心功能。
"""

import base64
import io
import logging
from typing import Dict, Any, Optional, Tuple, List
from pathlib import Path

try:
    import pyautogui
    import pygetwindow as gw
    from PIL import Image, ImageGrab
    import time
    # 尝试导入Windows API用于多显示器支持
    try:
        import win32gui
        import win32ui
        import win32con
        import win32api
        HAS_WIN32 = True
    except ImportError:
        HAS_WIN32 = False
except ImportError as e:
    raise ImportError(
        f"Required dependency not found: {e}. "
        "Please install with: uv add pyautogui pygetwindow pillow"
    )

# 配置pyautogui
pyautogui.FAILSAFE = True  # 鼠标移到左上角时停止
pyautogui.PAUSE = 0.1  # 操作间隔


class DeskPilotCore:
    """DeskPilot 核心功能实现"""

    def __init__(self, visual_mode: bool = True, operation_delay: float = 0.5):
        """初始化DeskPilot核心

        Args:
            visual_mode: 是否启用可视化模式
            operation_delay: 操作间隔时间（秒）
        """
        self.logger = logging.getLogger(__name__)
        self.visual_mode = visual_mode
        self.operation_delay = operation_delay

        # 如果启用可视化模式，调整pyautogui设置
        if self.visual_mode:
            pyautogui.PAUSE = max(operation_delay, 0.3)  # 最小0.3秒间隔
            self.logger.info(f"DeskPilot Core initialized with visual mode enabled (delay: {operation_delay}s)")
        else:
            self.logger.info("DeskPilot Core initialized")
    
    def get_window_list(self) -> Dict[str, Any]:
        """获取所有窗口列表

        Returns:
            Dict: 包含成功状态和窗口信息列表
        """
        try:
            windows = gw.getAllWindows()
            window_list = []

            for window in windows:
                if window.title.strip():  # 只包含有标题的窗口
                    window_info = {
                        "title": window.title,
                        "x": window.left,
                        "y": window.top,
                        "width": window.width,
                        "height": window.height,
                        "visible": window.visible,
                        "is_minimized": window.isMinimized,
                        "is_maximized": window.isMaximized,
                        "is_active": window.isActive
                    }
                    window_list.append(window_info)

            self.logger.info(f"Found {len(window_list)} windows")
            return {
                "success": True,
                "windows": window_list,
                "count": len(window_list)
            }

        except Exception as e:
            self.logger.error(f"Error getting window list: {e}")
            return {
                "success": False,
                "error": str(e),
                "windows": []
            }
    
    def capture_window(self, window_title: Optional[str] = None) -> Dict[str, Any]:
        """捕获指定窗口或活动窗口的截图
        
        Args:
            window_title: 窗口标题，如果为None则截图活动窗口
            
        Returns:
            Dict: 包含截图base64编码、窗口信息的字典
        """
        try:
            if window_title:
                # 查找指定标题的窗口
                windows = gw.getWindowsWithTitle(window_title)
                if not windows:
                    window_list_result = self.get_window_list()
                    available_windows = []
                    if window_list_result['success']:
                        available_windows = [w["title"] for w in window_list_result['windows']]
                    return {
                        "success": False,
                        "error": f"Window with title '{window_title}' not found",
                        "available_windows": available_windows
                    }

                # 如果有多个匹配的窗口，选择最合适的一个
                if len(windows) > 1:
                    self.logger.debug(f"Found {len(windows)} windows with title '{window_title}', selecting best match")
                    # 优先选择激活的窗口
                    active_windows = [w for w in windows if w.isActive]
                    if active_windows:
                        window = active_windows[0]
                        self.logger.debug(f"Selected active window: {window.title}")
                    else:
                        # 优先选择尺寸合理的窗口（排除异常大的窗口）
                        reasonable_windows = [w for w in windows if 100 < w.width < 5000 and 100 < w.height < 3000]
                        if reasonable_windows:
                            window = reasonable_windows[0]
                            self.logger.debug(f"Selected reasonable-sized window: {window.title}")
                        else:
                            window = windows[0]
                            self.logger.debug(f"Selected first window: {window.title}")
                else:
                    window = windows[0]
            else:
                # 获取活动窗口
                window = gw.getActiveWindow()
                if not window:
                    return {
                        "success": False,
                        "error": "No active window found"
                    }
            
            # 尝试确保窗口可见，但不强制激活
            try:
                if window.isMinimized:
                    window.restore()
                    self.logger.debug(f"Restored minimized window: {window.title}")
            except Exception as restore_error:
                self.logger.warning(f"Failed to restore window '{window.title}': {restore_error}")

            # 获取窗口位置和大小，并验证信息准确性
            left, top, width, height = window.left, window.top, window.width, window.height
            self.logger.debug(f"Initial window bounds: left={left}, top={top}, width={width}, height={height}")

            # 强制验证窗口信息的准确性，使用Win32 API作为参考
            try:
                import win32gui
                hwnd = win32gui.FindWindow(None, window.title)
                if hwnd:
                    win32_rect = win32gui.GetWindowRect(hwnd)
                    win32_left, win32_top, win32_right, win32_bottom = win32_rect
                    win32_width = win32_right - win32_left
                    win32_height = win32_bottom - win32_top

                    self.logger.debug(f"Win32 API window bounds: left={win32_left}, top={win32_top}, width={win32_width}, height={win32_height}")

                    # 检查pygetwindow和Win32 API的差异
                    pos_diff = abs(left - win32_left) + abs(top - win32_top)
                    size_diff = abs(width - win32_width) + abs(height - win32_height)

                    if pos_diff > 50 or size_diff > 50:
                        self.logger.warning(f"Large discrepancy between pygetwindow and Win32 API: pos_diff={pos_diff}, size_diff={size_diff}")
                        self.logger.warning(f"Using Win32 API values instead")
                        left, top, width, height = win32_left, win32_top, win32_width, win32_height
                    else:
                        self.logger.debug("pygetwindow and Win32 API values are consistent")

            except Exception as win32_error:
                self.logger.debug(f"Win32 API validation failed: {win32_error}")

            # 验证窗口边界是否有效
            if width <= 0 or height <= 0 or width > 10000 or height > 10000:
                self.logger.warning(f"Invalid window dimensions: {width}x{height}")
                raise ValueError(f"Invalid window dimensions: {width}x{height}")

            self.logger.debug(f"Final window bounds: left={left}, top={top}, width={width}, height={height}")

            # 分析窗口位置，判断在哪个显示器上
            position_analysis = self.analyze_window_position(left, top, width, height)
            if not position_analysis["success"]:
                self.logger.warning(f"Failed to analyze window position: {position_analysis.get('error')}")
                # 回退到原有逻辑
                screen_width, screen_height = pyautogui.size()
                is_on_secondary = left >= screen_width or left + width <= 0 or top >= screen_height or top + height <= 0
            else:
                is_on_secondary = not position_analysis["is_on_primary"]
                target_monitor = position_analysis["target_monitor"]
                self.logger.info(f"Window '{window.title}' is on monitor: {target_monitor['device_name']} "
                               f"(Primary: {target_monitor['is_primary']})")

            # 进行截图
            screenshot = None

            # 根据窗口位置选择截图方法
            if is_on_secondary:
                self.logger.info(f"Window is on secondary monitor, using advanced capture methods")

                # 尝试激活窗口（忽略激活错误）
                try:
                    if not window.isActive:
                        window.activate()
                        time.sleep(0.5)  # 等待窗口激活
                        self.logger.debug("Window activated successfully")
                except Exception as activate_error:
                    self.logger.debug(f"Window activation failed (continuing anyway): {activate_error}")

                # 对于多显示器情况，尝试多种截图方法
                if HAS_WIN32:
                    self.logger.info("Using Windows API for multi-monitor capture")
                    try:
                        # 方法1: 使用简单的桌面BitBlt方法 (之前成功的实现)
                        self.logger.debug("Using simple desktop BitBlt method")

                        # 激活窗口确保可见
                        try:
                            if not window.isActive:
                                window.activate()
                                time.sleep(0.5)
                                self.logger.debug("Window activated")
                        except Exception as activate_error:
                            self.logger.debug(f"Window activation failed (not critical): {activate_error}")

                        # 创建设备上下文
                        hdesktop = win32gui.GetDesktopWindow()
                        desktop_dc = win32gui.GetWindowDC(hdesktop)
                        img_dc = win32ui.CreateDCFromHandle(desktop_dc)
                        mem_dc = img_dc.CreateCompatibleDC()

                        # 创建位图
                        win32_screenshot = win32ui.CreateBitmap()
                        win32_screenshot.CreateCompatibleBitmap(img_dc, width, height)
                        mem_dc.SelectObject(win32_screenshot)

                        # 复制窗口区域到位图 (使用之前成功的方法)
                        mem_dc.BitBlt((0, 0), (width, height), img_dc, (left, top), win32con.SRCCOPY)

                        # 获取位图数据
                        bmpinfo = win32_screenshot.GetInfo()
                        bmpstr = win32_screenshot.GetBitmapBits(True)

                        # 转换为PIL Image
                        screenshot = Image.frombuffer(
                            'RGB',
                            (bmpinfo['bmWidth'], bmpinfo['bmHeight']),
                            bmpstr, 'raw', 'BGRX', 0, 1
                        )

                        # 清理资源
                        mem_dc.DeleteDC()
                        img_dc.DeleteDC()
                        win32gui.ReleaseDC(hdesktop, desktop_dc)
                        win32gui.DeleteObject(win32_screenshot.GetHandle())

                        self.logger.info(f"Successfully captured window with simple BitBlt: {screenshot.size}")

                        # 方法2: 如果简单方法失败，尝试其他方法
                        if screenshot is None:
                            self.logger.info("Trying desktop region capture")
                            # 创建设备上下文
                            hdesktop = win32gui.GetDesktopWindow()
                            desktop_dc = win32gui.GetWindowDC(hdesktop)
                            img_dc = win32ui.CreateDCFromHandle(desktop_dc)
                            mem_dc = img_dc.CreateCompatibleDC()

                            # 创建位图
                            win32_screenshot = win32ui.CreateBitmap()
                            win32_screenshot.CreateCompatibleBitmap(img_dc, width, height)
                            mem_dc.SelectObject(win32_screenshot)

                            # 复制桌面区域到位图
                            mem_dc.BitBlt((0, 0), (width, height), img_dc, (left, top), win32con.SRCCOPY)

                            # 获取位图数据
                            bmpinfo = win32_screenshot.GetInfo()
                            bmpstr = win32_screenshot.GetBitmapBits(True)

                            # 转换为PIL Image
                            screenshot = Image.frombuffer(
                                'RGB',
                                (bmpinfo['bmWidth'], bmpinfo['bmHeight']),
                                bmpstr, 'raw', 'BGRX', 0, 1
                            )

                            # 清理资源
                            mem_dc.DeleteDC()
                            img_dc.DeleteDC()
                            win32gui.ReleaseDC(hdesktop, desktop_dc)
                            win32gui.DeleteObject(win32_screenshot.GetHandle())

                            self.logger.info(f"Successfully captured multi-monitor window with desktop region: {screenshot.size}")

                    except Exception as win32_error:
                        self.logger.warning(f"Windows API capture failed: {win32_error}")
                        screenshot = None

                # 如果Windows API失败或不可用，使用PIL ImageGrab作为回退
                if screenshot is None:
                    self.logger.warning("Falling back to PIL ImageGrab for multi-monitor capture")
                    try:
                        screenshot = ImageGrab.grab(bbox=(left, top, left + width, top + height))
                        self.logger.info(f"PIL ImageGrab capture successful: {screenshot.size}")
                    except Exception as pil_error:
                        self.logger.warning(f"PIL ImageGrab failed: {pil_error}")
                        # 最后回退到pyautogui全屏截图
                        screenshot = pyautogui.screenshot()
                        self.logger.warning("Using pyautogui full screen as final fallback")
            else:
                # 窗口在主显示器上，正常截图
                self.logger.info(f"Window is on primary monitor, using standard capture")
                screenshot = pyautogui.screenshot(region=(left, top, width, height))
                self.logger.debug(f"Successfully captured screenshot of window: {window.title}")

            # 确保我们有一个有效的截图
            if screenshot is None:
                self.logger.warning("All capture methods failed, using full screen as final fallback")
                screenshot = pyautogui.screenshot()
            
            # 转换为base64
            buffer = io.BytesIO()
            screenshot.save(buffer, format='PNG')
            image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
            
            result = {
                "success": True,
                "window_title": window.title,
                "window_info": {
                    "left": window.left,
                    "top": window.top,
                    "width": window.width,
                    "height": window.height
                },
                "screenshot_base64": image_base64,
                "screenshot_format": "PNG"
            }

            # 添加位置分析信息（如果可用）
            if position_analysis["success"]:
                result["position_analysis"] = {
                    "target_monitor": position_analysis["target_monitor"],
                    "is_on_primary": position_analysis["is_on_primary"],
                    "is_multi_monitor": position_analysis["is_multi_monitor"],
                    "relative_position": position_analysis["relative_position"]
                }

            self.logger.info(f"Successfully captured window: {window.title}")
            return result
            
        except Exception as e:
            self.logger.error(f"Error capturing window: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def perform_ui_action(self, action_type: str, **params) -> Dict[str, Any]:
        """执行 UI 操作，如点击、输入文本等

        Args:
            action_type: 操作类型 ("click", "type", "key", "scroll")
            **params: 操作参数

        Returns:
            Dict: 操作结果
        """
        try:
            # 可视化模式下的操作前提示
            if self.visual_mode:
                self.logger.info(f"🎯 准备执行 {action_type} 操作: {params}")

            if action_type == "click":
                return self._perform_click_action(**params)
            elif action_type == "type":
                return self._perform_type_action(**params)
            elif action_type == "key":
                return self._perform_key_action(**params)
            elif action_type == "scroll":
                return self._perform_scroll_action(**params)
            else:
                return {
                    "success": False,
                    "error": f"Unknown action type: {action_type}. "
                           f"Supported: click, type, key, scroll"
                }

        except Exception as e:
            self.logger.error(f"Error performing UI action: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _perform_click_action(self, **params) -> Dict[str, Any]:
        """执行点击操作"""
        x = params.get("x")
        y = params.get("y")
        button = params.get("button", "left")
        clicks = params.get("clicks", 1)

        if x is None or y is None:
            return {
                "success": False,
                "error": "Click action requires 'x' and 'y' coordinates"
            }

        # 可视化模式下的增强操作
        if self.visual_mode:
            # 先移动鼠标到目标位置，让用户看到
            self.logger.info(f"🖱️ 移动鼠标到位置 ({x}, {y})")
            pyautogui.moveTo(x, y, duration=0.5)

            # 短暂停顿让用户看清位置
            import time
            time.sleep(0.3)

            # 执行点击
            self.logger.info(f"👆 执行{button}键点击 ({clicks}次)")
            pyautogui.click(x, y, clicks=clicks, button=button)

            # 操作后停顿
            time.sleep(self.operation_delay)
        else:
            pyautogui.click(x, y, clicks=clicks, button=button)

        return {
            "success": True,
            "action": "click",
            "coordinates": [x, y],
            "button": button,
            "clicks": clicks,
            "visual_mode": self.visual_mode
        }

    def _perform_type_action(self, **params) -> Dict[str, Any]:
        """执行文本输入操作"""
        text = params.get("text")
        interval = params.get("interval", 0.05 if self.visual_mode else 0.01)

        if not text:
            return {
                "success": False,
                "error": "Type action requires 'text' parameter"
            }

        if self.visual_mode:
            self.logger.info(f"⌨️ 输入文本: '{text}' (字符间隔: {interval}s)")
            # 可视化模式下使用较慢的输入速度
            pyautogui.typewrite(text, interval=interval)

            import time
            time.sleep(self.operation_delay)
        else:
            pyautogui.typewrite(text, interval=interval)

        return {
            "success": True,
            "action": "type",
            "text": text,
            "length": len(text),
            "interval": interval,
            "visual_mode": self.visual_mode
        }

    def _perform_key_action(self, **params) -> Dict[str, Any]:
        """执行按键操作"""
        key = params.get("key")

        if not key:
            return {
                "success": False,
                "error": "Key action requires 'key' parameter"
            }

        if self.visual_mode:
            self.logger.info(f"🔑 按下按键: {key}")

            # 检查是否是组合键（包含"+"）
            if '+' in key:
                # 组合键，使用hotkey函数
                keys = [k.strip() for k in key.split('+')]
                self.logger.debug(f"执行组合键: {keys}")
                pyautogui.hotkey(*keys)
            else:
                # 单个按键，使用press函数
                self.logger.debug(f"执行单个按键: {key}")
                pyautogui.press(key)

            import time
            time.sleep(self.operation_delay)
        else:
            # 非可视化模式下也需要同样的逻辑
            if '+' in key:
                keys = [k.strip() for k in key.split('+')]
                pyautogui.hotkey(*keys)
            else:
                pyautogui.press(key)

        return {
            "success": True,
            "action": "key",
            "key": key,
            "visual_mode": self.visual_mode
        }

    def _perform_scroll_action(self, **params) -> Dict[str, Any]:
        """执行滚动操作"""
        x = params.get("x")
        y = params.get("y")
        clicks = params.get("clicks", 3)

        if x is None or y is None:
            return {
                "success": False,
                "error": "Scroll action requires 'x' and 'y' coordinates"
            }

        if self.visual_mode:
            self.logger.info(f"🖱️ 移动到位置 ({x}, {y}) 准备滚动")
            pyautogui.moveTo(x, y, duration=0.3)

            import time
            time.sleep(0.2)

            self.logger.info(f"📜 滚动 {clicks} 步")
            pyautogui.scroll(clicks, x=x, y=y)

            time.sleep(self.operation_delay)
        else:
            pyautogui.scroll(clicks, x=x, y=y)

        return {
            "success": True,
            "action": "scroll",
            "coordinates": [x, y],
            "clicks": clicks,
            "visual_mode": self.visual_mode
        }

    def set_visual_mode(self, enabled: bool, operation_delay: float = 0.5) -> Dict[str, Any]:
        """设置可视化模式

        Args:
            enabled: 是否启用可视化模式
            operation_delay: 操作间隔时间

        Returns:
            Dict: 设置结果
        """
        try:
            self.visual_mode = enabled
            self.operation_delay = operation_delay

            if enabled:
                pyautogui.PAUSE = max(operation_delay, 0.3)
                self.logger.info(f"✅ 可视化模式已启用 (操作延迟: {operation_delay}s)")
            else:
                pyautogui.PAUSE = 0.1
                self.logger.info("❌ 可视化模式已禁用")

            return {
                "success": True,
                "visual_mode": self.visual_mode,
                "operation_delay": self.operation_delay,
                "message": f"Visual mode {'enabled' if enabled else 'disabled'}"
            }
        except Exception as e:
            self.logger.error(f"Error setting visual mode: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def get_mouse_position(self) -> Dict[str, Any]:
        """获取当前鼠标位置

        Returns:
            Dict: 鼠标位置信息
        """
        try:
            x, y = pyautogui.position()

            if self.visual_mode:
                self.logger.info(f"🖱️ 当前鼠标位置: ({x}, {y})")

            return {
                "success": True,
                "x": x,
                "y": y,
                "position": [x, y]
            }
        except Exception as e:
            self.logger.error(f"Error getting mouse position: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def automate_ide_terminal_workflow(self, ide_window_title: str, command: str) -> Dict[str, Any]:
        """自动化IDE终端工作流

        Args:
            ide_window_title: IDE窗口标题（部分匹配）
            command: 要在终端中执行的命令

        Returns:
            Dict: 工作流执行结果
        """
        try:
            workflow_steps = []

            # 步骤1: 查找并激活IDE窗口
            if self.visual_mode:
                self.logger.info(f"🔍 查找IDE窗口: {ide_window_title}")

            windows = gw.getWindowsWithTitle(ide_window_title)
            if not windows:
                # 尝试模糊匹配
                all_windows = gw.getAllWindows()
                matching_windows = [w for w in all_windows if ide_window_title.lower() in w.title.lower()]
                if not matching_windows:
                    return {
                        "success": False,
                        "error": f"未找到包含 '{ide_window_title}' 的窗口",
                        "workflow_steps": workflow_steps
                    }
                windows = matching_windows

            ide_window = windows[0]
            workflow_steps.append(f"找到IDE窗口: {ide_window.title}")

            # 步骤2: 激活IDE窗口
            if self.visual_mode:
                self.logger.info(f"🎯 激活IDE窗口: {ide_window.title}")

            try:
                ide_window.activate()
                workflow_steps.append("激活IDE窗口")

                if self.visual_mode:
                    import time
                    time.sleep(1.0)  # 等待窗口激活

            except Exception as e:
                self.logger.warning(f"无法激活窗口，尝试点击: {e}")
                # 如果无法激活，尝试点击窗口
                center_x = ide_window.left + ide_window.width // 2
                center_y = ide_window.top + ide_window.height // 2
                pyautogui.click(center_x, center_y)
                workflow_steps.append("点击激活IDE窗口")

            # 步骤3: 打开终端 (通常是 Ctrl+` 或 Ctrl+Shift+`)
            if self.visual_mode:
                self.logger.info("⌨️ 打开终端 (Ctrl+`)")

            pyautogui.hotkey('ctrl', '`')
            workflow_steps.append("打开终端 (Ctrl+`)")

            if self.visual_mode:
                import time
                time.sleep(1.5)  # 等待终端打开

            # 步骤4: 执行命令
            if self.visual_mode:
                self.logger.info(f"💻 执行命令: {command}")

            # 清空当前行（以防有残留内容）
            pyautogui.hotkey('ctrl', 'c')  # 取消当前操作
            if self.visual_mode:
                import time
                time.sleep(0.3)

            # 输入命令
            pyautogui.typewrite(command, interval=0.05 if self.visual_mode else 0.01)
            workflow_steps.append(f"输入命令: {command}")

            if self.visual_mode:
                import time
                time.sleep(0.5)

            # 按回车执行
            pyautogui.press('enter')
            workflow_steps.append("执行命令 (Enter)")

            if self.visual_mode:
                self.logger.info("✅ 工作流执行完成")
                import time
                time.sleep(1.0)

            return {
                "success": True,
                "ide_window": ide_window.title,
                "command": command,
                "workflow_steps": workflow_steps,
                "message": "IDE终端工作流执行完成"
            }

        except Exception as e:
            self.logger.error(f"IDE终端工作流执行失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "workflow_steps": workflow_steps
            }
    
    def get_screen_size(self) -> Dict[str, Any]:
        """获取屏幕尺寸

        Returns:
            Dict: 屏幕宽度和高度
        """
        try:
            size = pyautogui.size()
            return {
                "success": True,
                "width": size.width,
                "height": size.height
            }
        except Exception as e:
            self.logger.error(f"Error getting screen size: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def get_monitor_info(self) -> Dict[str, Any]:
        """获取所有显示器信息

        Returns:
            Dict: 包含所有显示器信息的字典
        """
        try:
            monitors = []

            if HAS_WIN32:
                # 使用Windows API获取详细的显示器信息
                try:
                    # EnumDisplayMonitors 返回 (hMonitor, hdcMonitor, rect) 的列表
                    monitor_list = win32api.EnumDisplayMonitors(None, None)

                    for hmonitor, hdc, rect in monitor_list:
                        try:
                            monitor_info = win32api.GetMonitorInfo(hmonitor)
                            monitor_data = {
                                "handle": hmonitor,
                                "left": rect[0],
                                "top": rect[1],
                                "right": rect[2],
                                "bottom": rect[3],
                                "width": rect[2] - rect[0],
                                "height": rect[3] - rect[1],
                                "is_primary": monitor_info["Flags"] == 1,
                                "device_name": monitor_info.get("Device", "Unknown")
                            }
                            monitors.append(monitor_data)
                        except Exception as e:
                            self.logger.warning(f"Error processing monitor {hmonitor}: {e}")

                    self.logger.debug(f"Found {len(monitors)} monitors using Windows API")

                except Exception as enum_error:
                    self.logger.warning(f"EnumDisplayMonitors failed: {enum_error}")
                    # 如果枚举失败，回退到基本方法
                    monitors = []

            # 如果Windows API失败或不可用，使用基本的屏幕信息
            if not monitors:
                primary_size = pyautogui.size()
                monitors.append({
                    "handle": None,
                    "left": 0,
                    "top": 0,
                    "right": primary_size.width,
                    "bottom": primary_size.height,
                    "width": primary_size.width,
                    "height": primary_size.height,
                    "is_primary": True,
                    "device_name": "Primary Monitor"
                })

                if HAS_WIN32:
                    self.logger.warning("Windows API enumeration failed, using basic screen info")
                else:
                    self.logger.warning("Windows API not available, using basic screen info")

            return {
                "success": True,
                "monitors": monitors,
                "count": len(monitors)
            }

        except Exception as e:
            self.logger.error(f"Error getting monitor info: {e}")
            return {
                "success": False,
                "error": str(e),
                "monitors": []
            }

    def analyze_window_position(self, window_left: int, window_top: int,
                              window_width: int, window_height: int) -> Dict[str, Any]:
        """分析窗口位置，判断窗口在哪个显示器上

        Args:
            window_left: 窗口左边界
            window_top: 窗口上边界
            window_width: 窗口宽度
            window_height: 窗口高度

        Returns:
            Dict: 窗口位置分析结果
        """
        try:
            # 获取显示器信息
            monitor_result = self.get_monitor_info()
            if not monitor_result["success"]:
                return {
                    "success": False,
                    "error": "Failed to get monitor information"
                }

            monitors = monitor_result["monitors"]
            window_right = window_left + window_width
            window_bottom = window_top + window_height

            # 计算窗口中心点
            window_center_x = window_left + window_width // 2
            window_center_y = window_top + window_height // 2

            # 查找窗口所在的显示器
            target_monitor = None
            max_overlap_area = 0

            for monitor in monitors:
                # 计算窗口与显示器的重叠区域
                overlap_left = max(window_left, monitor["left"])
                overlap_top = max(window_top, monitor["top"])
                overlap_right = min(window_right, monitor["right"])
                overlap_bottom = min(window_bottom, monitor["bottom"])

                if overlap_left < overlap_right and overlap_top < overlap_bottom:
                    overlap_area = (overlap_right - overlap_left) * (overlap_bottom - overlap_top)

                    if overlap_area > max_overlap_area:
                        max_overlap_area = overlap_area
                        target_monitor = monitor

            # 如果没有找到重叠区域，使用中心点判断
            if target_monitor is None:
                for monitor in monitors:
                    if (monitor["left"] <= window_center_x < monitor["right"] and
                        monitor["top"] <= window_center_y < monitor["bottom"]):
                        target_monitor = monitor
                        break

            # 如果还是没找到，使用主显示器
            if target_monitor is None:
                for monitor in monitors:
                    if monitor["is_primary"]:
                        target_monitor = monitor
                        break

            if target_monitor is None:
                return {
                    "success": False,
                    "error": "No suitable monitor found for window"
                }

            # 计算窗口在目标显示器上的相对位置
            relative_left = window_left - target_monitor["left"]
            relative_top = window_top - target_monitor["top"]

            result = {
                "success": True,
                "window_bounds": {
                    "left": window_left,
                    "top": window_top,
                    "right": window_right,
                    "bottom": window_bottom,
                    "width": window_width,
                    "height": window_height,
                    "center_x": window_center_x,
                    "center_y": window_center_y
                },
                "target_monitor": target_monitor,
                "relative_position": {
                    "left": relative_left,
                    "top": relative_top
                },
                "overlap_area": max_overlap_area,
                "is_on_primary": target_monitor["is_primary"],
                "is_multi_monitor": len(monitors) > 1
            }

            self.logger.debug(f"Window position analysis: monitor={target_monitor['device_name']}, "
                            f"primary={target_monitor['is_primary']}, overlap={max_overlap_area}")

            return result

        except Exception as e:
            self.logger.error(f"Error analyzing window position: {e}")
            return {
                "success": False,
                "error": str(e)
            }
