"""
DeskPilot MCP Server - Model Context Protocol 服务器实现

提供MCP协议接口，让AI客户端可以调用桌面自动化功能。
"""

import asyncio
import logging
from typing import Any, Dict, Optional

try:
    import mcp.types as types
    from mcp.server import Server
    from mcp.server.stdio import stdio_server
except ImportError as e:
    raise ImportError(
        f"MCP SDK not found: {e}. "
        "Please install with: uv add mcp"
    )

from .core import DeskPilotCore


class DeskPilotMcpServer:
    """DeskPilot MCP 服务器实现"""

    def __init__(self, visual_mode: bool = True, operation_delay: float = 0.5):
        """初始化MCP服务器

        Args:
            visual_mode: 是否启用可视化模式
            operation_delay: 操作间隔时间
        """
        self.logger = logging.getLogger(__name__)
        self.core = DeskPilotCore(visual_mode=visual_mode, operation_delay=operation_delay)

        # 创建MCP服务器实例，使用更明确的名称和版本信息
        self.server = Server("DeskPilot")

        # 注册工具
        self._register_tools()

        mode_info = f" (可视化模式: {'启用' if visual_mode else '禁用'})"
        self.logger.info(f"DeskPilot MCP Server initialized{mode_info}")
    
    def _register_tools(self):
        """注册 MCP 工具"""
        
        @self.server.list_tools()
        async def list_tools() -> list[types.Tool]:
            """列出所有可用的工具"""
            return [
                types.Tool(
                    name="deskpilot-capture-window",
                    description="[DeskPilot] Capture screenshot of a specific window or active window for desktop automation",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "window_title": {
                                "type": "string",
                                "description": "Title of the window to capture (optional, captures active window if not provided)"
                            }
                        }
                    }
                ),
                types.Tool(
                    name="deskpilot-ui-action",
                    description="[DeskPilot] Perform UI actions like click, type, key press, or scroll for desktop automation",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "action_type": {
                                "type": "string",
                                "enum": ["click", "type", "key", "scroll"],
                                "description": "Type of UI action to perform"
                            },
                            "x": {
                                "type": "number",
                                "description": "X coordinate (required for click and scroll)"
                            },
                            "y": {
                                "type": "number",
                                "description": "Y coordinate (required for click and scroll)"
                            },
                            "text": {
                                "type": "string",
                                "description": "Text to type (required for type action)"
                            },
                            "key": {
                                "type": "string",
                                "description": "Key to press (required for key action)"
                            },
                            "button": {
                                "type": "string",
                                "enum": ["left", "right", "middle"],
                                "description": "Mouse button for click action (default: left)"
                            },
                            "clicks": {
                                "type": "number",
                                "description": "Number of clicks/scroll steps (default: 1 for click, 3 for scroll)"
                            },
                            "interval": {
                                "type": "number",
                                "description": "Interval between characters when typing (default: 0.01)"
                            }
                        },
                        "required": ["action_type"]
                    }
                ),
                types.Tool(
                    name="deskpilot-list-windows",
                    description="[DeskPilot] Get a list of all visible windows with their titles and properties for desktop automation",
                    inputSchema={
                        "type": "object",
                        "properties": {}
                    }
                ),
                types.Tool(
                    name="deskpilot-get-screen-size",
                    description="[DeskPilot] Get the screen dimensions for desktop automation",
                    inputSchema={
                        "type": "object",
                        "properties": {}
                    }
                ),
                types.Tool(
                    name="deskpilot-get-monitor-info",
                    description="[DeskPilot] Get detailed information about all monitors/displays for multi-monitor desktop automation",
                    inputSchema={
                        "type": "object",
                        "properties": {}
                    }
                ),

                types.Tool(
                    name="deskpilot-set-visual-mode",
                    description="[DeskPilot] Enable or disable visual mode for desktop automation (shows real-time operation effects)",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "enabled": {
                                "type": "boolean",
                                "description": "Whether to enable visual mode",
                                "default": True
                            },
                            "operation_delay": {
                                "type": "number",
                                "description": "Delay between operations in seconds (default: 0.5)",
                                "default": 0.5
                            }
                        },
                        "required": ["enabled"]
                    }
                ),
                types.Tool(
                    name="deskpilot-get-mouse-position",
                    description="[DeskPilot] Get current mouse cursor position for desktop automation",
                    inputSchema={
                        "type": "object",
                        "properties": {}
                    }
                ),
                types.Tool(
                    name="deskpilot-automate-ide-terminal",
                    description="[DeskPilot] Automate IDE terminal workflow: find IDE window, open terminal, and execute command",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "ide_window_title": {
                                "type": "string",
                                "description": "IDE window title or partial title to search for"
                            },
                            "command": {
                                "type": "string",
                                "description": "Command to execute in the terminal"
                            }
                        },
                        "required": ["ide_window_title", "command"]
                    }
                )
            ]
        
        @self.server.call_tool()
        async def call_tool(name: str, arguments: dict) -> list[types.TextContent]:
            """处理工具调用"""
            try:
                if name == "deskpilot-capture-window":
                    window_title = arguments.get("window_title")
                    result = self.core.capture_window(window_title)

                    if result.get("success"):
                        return [
                            types.TextContent(
                                type="text",
                                text=f"[DeskPilot] Successfully captured window: {result['window_title']}\n"
                                     f"Window size: {result['window_info']['width']}x{result['window_info']['height']}\n"
                                     f"Screenshot format: {result['screenshot_format']}\n"
                                     f"Screenshot data: data:image/png;base64,{result['screenshot_base64']}"
                            )
                        ]
                    else:
                        return [
                            types.TextContent(
                                type="text",
                                text=f"[DeskPilot] Failed to capture window: {result.get('error', 'Unknown error')}"
                            )
                        ]

                elif name == "deskpilot-ui-action":
                    action_type = arguments.get("action_type")
                    if not action_type:
                        return [
                            types.TextContent(
                                type="text",
                                text="[DeskPilot] Error: action_type is required"
                            )
                        ]

                    # 从arguments中移除action_type，避免重复传递
                    ui_args = {k: v for k, v in arguments.items() if k != "action_type"}
                    result = self.core.perform_ui_action(action_type, **ui_args)

                    if result.get("success"):
                        return [
                            types.TextContent(
                                type="text",
                                text=f"[DeskPilot] Successfully performed {action_type} action: {result}"
                            )
                        ]
                    else:
                        return [
                            types.TextContent(
                                type="text",
                                text=f"[DeskPilot] Failed to perform {action_type} action: {result.get('error', 'Unknown error')}"
                            )
                        ]
                
                elif name == "deskpilot-list-windows":
                    result = self.core.get_window_list()

                    if result.get("success"):
                        windows = result.get("windows", [])
                        window_info = "\n".join([
                            f"- {w['title']} ({w['width']}x{w['height']}) {'[Active]' if w.get('is_active', False) else ''}"
                            for w in windows
                        ])

                        return [
                            types.TextContent(
                                type="text",
                                text=f"[DeskPilot] Found {len(windows)} windows:\n{window_info}"
                            )
                        ]
                    else:
                        return [
                            types.TextContent(
                                type="text",
                                text=f"[DeskPilot] Failed to get window list: {result.get('error', 'Unknown error')}"
                            )
                        ]

                elif name == "deskpilot-get-screen-size":
                    result = self.core.get_screen_size()

                    if result.get("success"):
                        return [
                            types.TextContent(
                                type="text",
                                text=f"[DeskPilot] Screen size: {result['width']}x{result['height']}"
                            )
                        ]
                    else:
                        return [
                            types.TextContent(
                                type="text",
                                text=f"[DeskPilot] Failed to get screen size: {result.get('error', 'Unknown error')}"
                            )
                        ]
                


                elif name == "deskpilot-set-visual-mode":
                    enabled = arguments.get("enabled", True)  # 默认启用可视化模式
                    operation_delay = arguments.get("operation_delay", 0.5)

                    result = self.core.set_visual_mode(enabled, operation_delay)

                    if result.get("success"):
                        return [
                            types.TextContent(
                                type="text",
                                text=f"[DeskPilot] Visual mode {'enabled' if enabled else 'disabled'}\n"
                                     f"Operation delay: {operation_delay}s\n"
                                     f"Message: {result.get('message', '')}"
                            )
                        ]
                    else:
                        return [
                            types.TextContent(
                                type="text",
                                text=f"[DeskPilot] Failed to set visual mode: {result.get('error', 'Unknown error')}"
                            )
                        ]

                elif name == "deskpilot-get-mouse-position":
                    result = self.core.get_mouse_position()

                    if result.get("success"):
                        return [
                            types.TextContent(
                                type="text",
                                text=f"[DeskPilot] Current mouse position: ({result['x']}, {result['y']})"
                            )
                        ]
                    else:
                        return [
                            types.TextContent(
                                type="text",
                                text=f"[DeskPilot] Failed to get mouse position: {result.get('error', 'Unknown error')}"
                            )
                        ]

                elif name == "deskpilot-automate-ide-terminal":
                    ide_window_title = arguments.get("ide_window_title")
                    command = arguments.get("command")

                    result = self.core.automate_ide_terminal_workflow(ide_window_title, command)

                    if result.get("success"):
                        workflow_steps = "\n".join([f"  • {step}" for step in result.get('workflow_steps', [])])
                        return [
                            types.TextContent(
                                type="text",
                                text=f"[DeskPilot] IDE Terminal Workflow Completed Successfully\n"
                                     f"IDE Window: {result.get('ide_window', 'Unknown')}\n"
                                     f"Command: {result.get('command', 'Unknown')}\n"
                                     f"Workflow Steps:\n{workflow_steps}\n"
                                     f"Message: {result.get('message', '')}"
                            )
                        ]
                    else:
                        workflow_steps = "\n".join([f"  • {step}" for step in result.get('workflow_steps', [])])
                        return [
                            types.TextContent(
                                type="text",
                                text=f"[DeskPilot] IDE Terminal Workflow Failed\n"
                                     f"Error: {result.get('error', 'Unknown error')}\n"
                                     f"Completed Steps:\n{workflow_steps}"
                            )
                        ]

                else:
                    return [
                        types.TextContent(
                            type="text",
                            text=f"Unknown tool: {name}"
                        )
                    ]
                    
            except Exception as e:
                self.logger.error(f"Error calling tool {name}: {e}")
                return [
                    types.TextContent(
                        type="text",
                        text=f"Error calling tool {name}: {str(e)}"
                    )
                ]
    
    async def run(self):
        """运行MCP服务器"""
        try:
            async with stdio_server() as (read_stream, write_stream):
                self.logger.info("Starting DeskPilot MCP Server...")
                await self.server.run(
                    read_stream,
                    write_stream,
                    self.server.create_initialization_options()
                )
        except Exception as e:
            self.logger.error(f"Error running MCP server: {e}")
            raise
