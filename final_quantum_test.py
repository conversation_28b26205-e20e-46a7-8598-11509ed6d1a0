#!/usr/bin/env python3
"""最终QuantumSeek截图测试 - 综合所有方法"""

import logging
from desk_pilot.core import DeskPilotCore
import pygetwindow as gw
import time
import base64
from PIL import Image
import os

# 设置详细日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')

def ensure_window_ready():
    """确保QuantumSeek窗口准备就绪"""
    print('🔧 准备QuantumSeek窗口...')
    
    # 查找窗口
    windows = gw.getWindowsWithTitle('QuantumSeek')
    if not windows:
        print('❌ 未找到QuantumSeek窗口，请确保应用程序正在运行')
        return None
    
    window = windows[0]
    print(f'🎯 找到窗口: {window.title}')
    
    try:
        # 1. 恢复窗口（如果最小化）
        if window.isMinimized:
            print('🔄 恢复最小化窗口...')
            window.restore()
            time.sleep(1)
        
        # 2. 激活窗口
        if not window.isActive:
            print('🔄 激活窗口...')
            window.activate()
            time.sleep(1)
        
        # 3. 确保窗口在前台
        print('🔄 将窗口置于前台...')
        try:
            import win32gui
            hwnd = win32gui.FindWindow(None, window.title)
            if hwnd:
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.5)
        except:
            pass
        
        # 4. 等待窗口稳定
        time.sleep(1)
        
        print(f'✅ 窗口准备完成')
        print(f'   位置: ({window.left}, {window.top})')
        print(f'   尺寸: {window.width} x {window.height}')
        print(f'   状态: 可见={window.visible}, 激活={window.isActive}')
        
        return window
        
    except Exception as e:
        print(f'❌ 窗口准备失败: {e}')
        return window

def test_deskpilot_screenshot(window):
    """测试DeskPilot截图功能"""
    print('\n📸 测试DeskPilot截图功能...')
    
    core = DeskPilotCore(visual_mode=True)
    
    # 进行截图
    result = core.capture_window(window.title)
    
    if result["success"]:
        # 保存截图
        screenshot_data = base64.b64decode(result["screenshot_base64"])
        with open("final_quantum_screenshot.png", "wb") as f:
            f.write(screenshot_data)
        
        print('✅ DeskPilot截图成功')
        print(f'   截图已保存: final_quantum_screenshot.png')
        
        # 分析截图
        analyze_screenshot("final_quantum_screenshot.png")
        
        return True
    else:
        print(f'❌ DeskPilot截图失败: {result["error"]}')
        return False

def analyze_screenshot(filename):
    """分析截图质量"""
    print(f'\n🔍 分析截图: {filename}')
    
    try:
        with Image.open(filename) as img:
            width, height = img.size
            pixels = list(img.getdata())
            total_pixels = len(pixels)
            
            # 基本统计
            non_black_pixels = 0
            purple_pixels = 0
            bright_pixels = 0
            r_sum = g_sum = b_sum = 0
            
            for pixel in pixels:
                r, g, b = pixel[:3]
                r_sum += r
                g_sum += g
                b_sum += b
                
                # 非黑色像素
                if r > 15 or g > 15 or b > 15:
                    non_black_pixels += 1
                
                # 紫色像素
                if b > 80 and b > r and b > g:
                    purple_pixels += 1
                
                # 明亮像素
                if (r + g + b) / 3 > 100:
                    bright_pixels += 1
            
            # 计算比例
            non_black_ratio = non_black_pixels / total_pixels
            purple_ratio = purple_pixels / total_pixels
            bright_ratio = bright_pixels / total_pixels
            avg_brightness = (r_sum + g_sum + b_sum) / (3 * total_pixels)
            
            print(f'   尺寸: {width} x {height}')
            print(f'   平均亮度: {avg_brightness:.1f}')
            print(f'   非黑色像素: {non_black_ratio:.2%}')
            print(f'   紫色像素: {purple_ratio:.2%}')
            print(f'   明亮像素: {bright_ratio:.2%}')
            
            # 质量评估
            print(f'\n📊 质量评估:')
            
            if avg_brightness < 30:
                print('   ✅ 深色主题检测正确')
            else:
                print('   ⚠️ 不是深色主题')
            
            if non_black_ratio > 0.4:
                print('   ✅ 有丰富的内容')
            elif non_black_ratio > 0.2:
                print('   ℹ️ 有一些内容')
            else:
                print('   ❌ 内容太少，可能截图失败')
            
            if purple_ratio > 0.001:
                print('   ✅ 检测到紫色主题元素')
            else:
                print('   ⚠️ 未检测到紫色主题')
            
            if bright_ratio > 0.01:
                print('   ✅ 有文本/UI元素')
            else:
                print('   ⚠️ 缺少明显的UI元素')
            
            # 区域分析
            print(f'\n📍 区域分析:')
            analyze_regions(img)
            
    except Exception as e:
        print(f'   ❌ 分析失败: {e}')

def analyze_regions(img):
    """分析图像区域"""
    width, height = img.size
    
    regions = {
        '左上': (0, 0, width//3, height//3),
        '中上': (width//3, 0, 2*width//3, height//3),
        '右上': (2*width//3, 0, width, height//3),
        '左中': (0, height//3, width//3, 2*height//3),
        '中央': (width//3, height//3, 2*width//3, 2*height//3),
        '右中': (2*width//3, height//3, width, 2*height//3),
        '左下': (0, 2*height//3, width//3, height),
        '中下': (width//3, 2*height//3, 2*width//3, height),
        '右下': (2*width//3, 2*height//3, width, height),
    }
    
    for region_name, (x1, y1, x2, y2) in regions.items():
        try:
            region = img.crop((x1, y1, x2, y2))
            pixels = list(region.getdata())
            
            if not pixels:
                continue
            
            non_black = sum(1 for p in pixels if sum(p[:3]) > 45)
            ratio = non_black / len(pixels)
            
            if ratio > 0.5:
                status = '✅ 有内容'
            elif ratio > 0.1:
                status = 'ℹ️ 少量内容'
            else:
                status = '❌ 无内容'
            
            print(f'   {region_name}: {ratio:.1%} {status}')
            
        except Exception as e:
            print(f'   {region_name}: 分析失败')

def compare_with_reference():
    """与参考截图比较"""
    print(f'\n📋 与用户参考截图比较:')
    print(f'   用户提供的QuantumSeek截图特征:')
    print(f'   ✅ 深色主题背景')
    print(f'   ✅ 紫色主题色调')
    print(f'   ✅ 左侧菜单栏')
    print(f'   ✅ 顶部搜索区域')
    print(f'   ✅ 中央进度显示')
    print(f'   ✅ 底部状态信息')
    print(f'   ✅ 清晰的UI文本')
    
    print(f'\n   🎯 判断标准:')
    print(f'   - 深色主题: 平均亮度 < 30')
    print(f'   - 有效内容: 非黑色像素 > 40%')
    print(f'   - 紫色主题: 紫色像素 > 0.1%')
    print(f'   - UI元素: 明亮像素 > 1%')
    print(f'   - 完整性: 各区域都有内容')

def main():
    """主测试函数"""
    print('🚀 QuantumSeek最终截图测试')
    print('=' * 60)
    
    try:
        # 1. 准备窗口
        window = ensure_window_ready()
        if not window:
            return
        
        # 2. 测试截图
        success = test_deskpilot_screenshot(window)
        
        # 3. 比较结果
        compare_with_reference()
        
        if success:
            print(f'\n✅ 测试完成!')
            print(f'📝 请查看 final_quantum_screenshot.png 并与用户参考截图比较')
            print(f'🔍 如果截图与参考图片一致，说明DeskPilot截图功能正常')
            print(f'❌ 如果不一致，可能需要进一步调试或使用其他截图方法')
        else:
            print(f'\n❌ 截图测试失败!')
            print(f'🔧 建议检查窗口状态、权限设置或尝试其他截图方法')
        
    except Exception as e:
        print(f'❌ 测试过程中发生错误: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
